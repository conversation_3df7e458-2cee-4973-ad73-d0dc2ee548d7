# frozen_string_literal: true

RSpec.describe "Sample Rand Integration" do
  before do
    perform_basic_setup do |config|
      config.traces_sample_rate = 0.5
      config.traces_sampler = lambda do |sampling_context|
        # Example of proper parent sampling inheritance
        parent_sample_rate = sampling_context[:parent_sample_rate]
        if parent_sample_rate
          parent_sample_rate
        else
          0.5
        end
      end
    end
  end

  describe "end-to-end propagated sampling" do
    it "maintains consistent sampling across distributed trace" do
      # Step 1: Start a new trace (head SDK)
      root_transaction = Sentry.start_transaction(name: "root", op: "http.server")

      # Verify root transaction has sample_rand
      sample_rand = root_transaction.instance_variable_get(:@sample_rand)
      expect(sample_rand).to be_a(Float)
      expect(sample_rand).to be >= 0.0
      expect(sample_rand).to be < 1.0

      # Step 2: Simulate outgoing HTTP request headers
      Sentry.get_current_scope.set_span(root_transaction)
      headers = Sentry.get_trace_propagation_headers

      expect(headers).to include("sentry-trace", "baggage")
      expect(headers["baggage"]).to include("sentry-sample_rand=")

      # Step 3: Simulate incoming request in downstream service
      sentry_trace = headers["sentry-trace"]
      baggage_header = headers["baggage"]

      # Create child transaction from incoming headers
      child_transaction = Sentry::Transaction.from_sentry_trace(
        sentry_trace,
        baggage: baggage_header
      )

      # Verify child transaction uses same sample_rand
      child_sample_rand = child_transaction.instance_variable_get(:@sample_rand)
      expect(child_sample_rand).to eq(sample_rand)

      # Step 4: Start child transaction and verify consistent sampling
      Sentry.get_current_scope.set_span(child_transaction)
      started_child = Sentry.start_transaction(transaction: child_transaction)

      # Both transactions should have same sampling decision
      expect(started_child.sampled).to eq(root_transaction.sampled)
      expect(started_child.effective_sample_rate).to eq(root_transaction.effective_sample_rate)

      # Step 5: Verify baggage propagation continues
      child_headers = Sentry.get_trace_propagation_headers
      expect(child_headers["baggage"]).to include("sentry-sample_rand=#{Sentry::Utils::SampleRand.format(sample_rand)}")
    end

    it "handles missing sample_rand gracefully" do
      # Simulate incoming trace from older SDK without sample_rand
      sentry_trace = "771a43a4192642f0b136d5159a501700-7c51afd529da4a2a-1"
      baggage_header = "sentry-trace_id=771a43a4192642f0b136d5159a501700,sentry-sample_rate=0.25"

      # Create transaction from incoming trace
      transaction = Sentry::Transaction.from_sentry_trace(sentry_trace, baggage: baggage_header)

      # Should generate deterministic sample_rand based on trace_id and sampling decision
      sample_rand = transaction.instance_variable_get(:@sample_rand)
      expect(sample_rand).to be_a(Float)
      expect(sample_rand).to be >= 0.0
      expect(sample_rand).to be < 1.0

      # For sampled=true and sample_rate=0.25, sample_rand should be < 0.25
      expect(sample_rand).to be < 0.25

      # Should be deterministic for same trace
      transaction2 = Sentry::Transaction.from_sentry_trace(sentry_trace, baggage: baggage_header)
      expect(transaction2.instance_variable_get(:@sample_rand)).to eq(sample_rand)
    end

    it "works with PropagationContext for tracing without performance" do
      # Simulate incoming request headers
      env = {
        "HTTP_SENTRY_TRACE" => "771a43a4192642f0b136d5159a501700-7c51afd529da4a2a-1",
        "HTTP_BAGGAGE" => "sentry-trace_id=771a43a4192642f0b136d5159a501700,sentry-sample_rand=0.123456"
      }

      # Create PropagationContext from incoming headers
      scope = Sentry.get_current_scope
      propagation_context = Sentry::PropagationContext.new(scope, env)

      # Should use sample_rand from incoming baggage
      expect(propagation_context.sample_rand).to eq(0.123456)

      # Should include sample_rand in outgoing baggage
      baggage = propagation_context.get_baggage
      expect(baggage.items["sample_rand"]).to eq("0.123456")
    end

    it "demonstrates deterministic sampling behavior" do
      # Test that same trace_id always produces same sampling decision
      trace_id = "771a43a4192642f0b136d5159a501700"

      # Create multiple transactions with same trace_id
      results = 5.times.map do
        transaction = Sentry::Transaction.new(trace_id: trace_id, hub: Sentry.get_current_hub)
        Sentry.start_transaction(transaction: transaction)
        transaction.sampled
      end

      # All should have same sampling decision
      expect(results.uniq.length).to eq(1)

      # Verify the sample_rand is deterministic too
      sample_rands = 5.times.map do
        transaction = Sentry::Transaction.new(trace_id: trace_id, hub: Sentry.get_current_hub)
        transaction.instance_variable_get(:@sample_rand)
      end

      expect(sample_rands.uniq.length).to eq(1)
    end

    it "works with custom traces_sampler" do
      # Set up custom traces_sampler that uses parent_sample_rate
      sampling_contexts = []
      Sentry.configuration.traces_sampler = lambda do |context|
        sampling_contexts << context
        context[:parent_sample_rate] || 0.5
      end

      # Create parent transaction with baggage
      baggage = Sentry::Baggage.new({ "sample_rate" => "0.75" })
      parent_transaction = Sentry::Transaction.new(
        hub: Sentry.get_current_hub,
        baggage: baggage,
        sample_rand: 0.6
      )

      # Start transaction through hub
      Sentry.start_transaction(transaction: parent_transaction)

      # Verify traces_sampler received parent_sample_rate
      expect(sampling_contexts.last[:parent_sample_rate]).to eq(0.75)

      # Verify sampling decision uses sample_rand
      # sample_rand (0.6) < sample_rate (0.75), so should be sampled
      expect(parent_transaction.sampled).to be true
    end

    it "handles edge case with invalid sample_rand in baggage" do
      # Simulate incoming trace with invalid sample_rand
      sentry_trace = "771a43a4192642f0b136d5159a501700-7c51afd529da4a2a-1"
      baggage_header = "sentry-trace_id=771a43a4192642f0b136d5159a501700,sentry-sample_rand=1.5"

      # Should fall back to generating sample_rand from trace_id
      transaction = Sentry::Transaction.from_sentry_trace(sentry_trace, baggage: baggage_header)

      sample_rand = transaction.instance_variable_get(:@sample_rand)
      expect(sample_rand).to be_a(Float)
      expect(sample_rand).to be >= 0.0
      expect(sample_rand).to be < 1.0

      # Should be deterministic based on trace_id
      expected = Sentry::Utils::SampleRand.generate_from_trace_id("771a43a4192642f0b136d5159a501700")
      expect(sample_rand).to eq(expected)
    end
  end
end
